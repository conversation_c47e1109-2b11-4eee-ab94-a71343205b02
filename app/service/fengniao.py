import hashlib
import hashlib
import json
import time
from datetime import datetime
from typing import Dict, Any
from typing import Optional

import redis
import requests
from pydantic import BaseModel, Field

from app.core.config import settings


class GetTokenRequest(BaseModel):
    """获取token请求参数"""
    grant_type: str = Field("authorization_code", description="授权模式")
    code: str = Field(..., description="授权码")
    app_id: str = Field(..., description="应用ID")
    merchant_id: str = Field(..., description="商户ID")
    signature: Optional[str] = Field(None, description="签名")
    timestamp: int = Field(..., description="时间戳")


# 初始化Redis客户端
redis_client = redis.Redis(
    host=settings.redis_host,
    port=settings.redis_port,
    db=settings.redis_db,
    decode_responses=True
)


class FengniaoClient:
    """蜂鸟即配API客户端"""

    def __init__(self, app_key: Optional[str] = None, app_secret: Optional[str] = None, api_url: Optional[str] = None):
        self.app_key = app_key or settings.fengniao_app_key
        self.app_secret = app_secret or settings.fengniao_app_secret
        self.api_url = api_url or settings.fengniao_api_url
        self.merchant_id = None or settings.fengniao_merchant_id

        if not self.app_key or not self.app_secret:
            raise ValueError("蜂鸟即配的AppKey和AppSecret不能为空")

    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """生成签名"""
        # 1. 对参数按key进行ASCII排序
        sorted_params = sorted(params.items(), key=lambda x: x[0])

        # 2. 拼接为"key=value&key=value"格式
        sign_list = []
        for key, value in sorted_params:
            sign_list.append('{}={}'.format(key, value))
        sign_before = self.app_secret + '&'.join(sign_list)
        # print ('signBefore:{} \n'.format(sign_before))

        # 3. HMAC-SHA256加密 + 转大写
        sha256 = hashlib.sha256()
        sha256.update(sign_before.encode('utf-8'))
        sign = sha256.hexdigest()
        # print ('sign:{} \n'.format(sign))
        return sign

    def _request(self, path: str, method: str = "POST", **kwargs) -> Dict[str, Any]:
        """发送请求到蜂鸟API"""
        # 1. 公共参数
        common_params = {
            "app_id": self.app_key,
            "timestamp": int(time.time() * 1000)  # 毫秒级时间戳
        }

        # 2. 合并参数
        params = {**common_params, **kwargs}

        # 3. 生成签名
        sign = self._generate_sign(params)
        params["signature"] = sign

        params = json.dumps(params)
        # print ('params:{} \n'.format(params))

        # 4. 发送请求
        url = f"{self.api_url}{path}"
        # print ('url: {} \n'.format(url))
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=params, timeout=10)
            else:
                response = requests.post(url, data=params, timeout=10)

            # 5. 解析响应
            return response.json()
        except Exception as e:
            return {"success": False, "code": -1, "msg": f"请求异常: {str(e)}"}

    def get_token(self, token_request: GetTokenRequest):
        """获取token（优先从redis读取）"""
        # 获取数据库会话
        try:
            # 1. 从redis查询是否存在有效token
            current_time = datetime.now()
            token_str = redis_client.get(f"{settings.redis_key_prefix}token")
            token_dict = json.loads(token_str) if token_str else {}
            # print (f'token_dict: {token_dict}')

            if token_dict and token_dict.get("access_token"):
                # 2. 如果存在有效token，直接返回
                return {
                    "success": True,
                    "business_data": json.dumps(token_dict)
                }

            # 3. 如果不存在，调用外部接口获取token
            req_data = token_request.dict(exclude_none=True)
            response = self._request("/anubis-webapi/openapi/token", **req_data)

            # 4. 将新token保存到数据库
            if response.get("success"):
                business_data = json.loads(response.get("business_data", '{}'))
                redis_client.set(
                    f"{settings.redis_key_prefix}token",
                    json.dumps(business_data),
                    ex=business_data.get("expire_in", 3600)
                )

            return response
        except Exception as e:
            return {"success": False, "code": -1, "msg": f"请求异常: {str(e)}"}

    def invoke(self, invoke_type: str, business_data: dict):
        """业务请求接口"""
        access_token = None
        token_request = GetTokenRequest(
            grant_type="authorization_code",
            code=settings.fengniao_authorization_code,
            app_id=settings.fengniao_app_key,
            merchant_id=settings.fengniao_merchant_id,
            timestamp=int(time.time() * 1000)
        )
        token_resp = self.get_token(token_request)

        if token_resp.get('success'):
            token_data = json.loads(token_resp.get('business_data', '{}'))
            access_token = token_data.get('access_token')

        request_data = {
            "access_token": access_token,
            "merchant_id": self.merchant_id,
            "business_data": json.dumps(business_data, ensure_ascii=False),
            "version": "1.0"
        }

        # 调用API
        response = self._request("/anubis-webapi/v3/invoke/{}".format(invoke_type), **request_data)

        # 转换为响应模型
        return response
