#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蜂鸟即配API调用示例脚本

本脚本展示如何调用蜂鸟即配API服务的各种接口，包括：
1. 预下单接口
2. 创建订单接口
3. 查询订单状态接口
4. 取消订单接口

使用前请确保：
1. 蜂鸟即配API服务已启动（默认端口8888）
2. 已正确配置环境变量
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

class FengniaoAPIClient:
    """蜂鸟即配API客户端示例"""
    
    def __init__(self, base_url: str = "http://localhost:8888"):
        self.base_url = base_url.rstrip('/')
        self.api_prefix = "/api"
        
    def _make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送HTTP请求的通用方法"""
        url = f"{self.base_url}{self.api_prefix}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=data, timeout=30)
            else:
                response = requests.post(url, json=data, timeout=30)
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            return {
                "code": 500,
                "status": "error",
                "message": f"请求异常: {str(e)}"
            }
        except json.JSONDecodeError as e:
            return {
                "code": 500,
                "status": "error",
                "message": f"响应解析异常: {str(e)}"
            }
    
    def pre_create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """预下单接口调用示例"""
        print("\n=== 调用预下单接口 ===")
        print(f"请求参数: {json.dumps(order_data, ensure_ascii=False, indent=2)}")
        
        result = self._make_request("POST", "/invoke_fengniao_api/preCreateOrder", order_data)
        
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result
    
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建订单接口调用示例"""
        print("\n=== 调用创建订单接口 ===")
        print(f"请求参数: {json.dumps(order_data, ensure_ascii=False, indent=2)}")
        
        result = self._make_request("POST", "/invoke_fengniao_api/createOrder", order_data)
        
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result
    
    def get_order_detail(self, order_query: Dict[str, Any]) -> Dict[str, Any]:
        """查询订单详情接口调用示例"""
        print("\n=== 调用查询订单接口 ===")
        print(f"请求参数: {json.dumps(order_query, ensure_ascii=False, indent=2)}")
        
        result = self._make_request("POST", "/invoke_fengniao_api/getOrderDetail", order_query)
        
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result
    
    def get_cancel_reason_list(self) -> Dict[str, Any]:
        """获取取消原因列表接口调用示例"""
        print("\n=== 调用获取取消原因列表接口 ===")
        
        result = self._make_request("POST", "/invoke_fengniao_api/getCancelReasonList", {})
        
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result
    
    def pre_cancel_order(self, cancel_data: Dict[str, Any]) -> Dict[str, Any]:
        """预取消订单接口调用示例"""
        print("\n=== 调用预取消订单接口 ===")
        print(f"请求参数: {json.dumps(cancel_data, ensure_ascii=False, indent=2)}")
        
        result = self._make_request("POST", "/invoke_fengniao_api/preCancelOrder", cancel_data)
        
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result
    
    def cancel_order(self, cancel_data: Dict[str, Any]) -> Dict[str, Any]:
        """正式取消订单接口调用示例"""
        print("\n=== 调用正式取消订单接口 ===")
        print(f"请求参数: {json.dumps(cancel_data, ensure_ascii=False, indent=2)}")
        
        result = self._make_request("POST", "/invoke_fengniao_api/cancelOrder", cancel_data)
        
        print(f"响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result

def create_sample_order_data() -> Dict[str, Any]:
    """创建示例订单数据"""
    # 获取当前时间戳（毫秒）
    current_timestamp = int(time.time() * 1000)
    # 期望取货时间（30分钟后）
    expect_fetch_time = current_timestamp + (30 * 60 * 1000)
    
    return {
        "partner_order_code": f"TEST_ORDER_{current_timestamp}",  # 商户订单号
        "chain_store_id": 461841708,
        "order_type": 1,  # 订单类型：1-即时单，3-预约单
        "position_source": 3,  # 位置来源：1-腾讯地图，2-百度地图，3-高德地图
        "expect_fetch_time": expect_fetch_time,  # 期望取货时间
        "order_add_time": current_timestamp,  # 订单创建时间
        
        # 收件人信息
        "receiver_name": "张三",
        "receiver_primary_phone": "13800138000",
        "receiver_address": "北京市海淀区上地街道彩虹大厦彩虹大厦(开拓路)",
        "receiver_longitude": 116.307892,  # 收件人经度
        "receiver_latitude": 40.039115,   # 收件人纬度
        
        # 商品信息
        "goods_total_amount_cent": 1000,  # 商品总金额（分）
        "goods_actual_amount_cent": 1000,  # 商品实际金额（分）
        "goods_weight": 1.5,  # 商品重量（kg）
        "goods_count": 2,  # 商品数量
        
        # 商品列表
        "goods_item_list": [
            {
                "item_id": "ITEM_001",
                "item_name": "测试商品1",
                "item_quantity": 1,
                "item_amount_cent": 3000,
                "item_actual_amount_cent": 2700,
                "item_remark": "商品备注1"
            },
            {
                "item_id": "ITEM_002",
                "item_name": "测试商品2",
                "item_quantity": 1,
                "item_amount_cent": 2000,
                "item_actual_amount_cent": 1800,
                "item_remark": "商品备注2"
            }
        ],
        
        "order_remark": "测试订单，请小心配送",  # 订单备注
        "order_tip_amount_cent": 500,  # 订单小费金额（分）
    }

def main():
    """主函数 - 演示完整的API调用流程"""
    print("蜂鸟即配API调用示例")
    print("=" * 50)
    
    # 初始化客户端
    client = FengniaoAPIClient()
    
    # 创建示例订单数据
    order_data = create_sample_order_data()
    partner_order_code = order_data["partner_order_code"]
    
    try:
        # 1. 预下单
        print("\n步骤1: 预下单")
        pre_order_result = client.pre_create_order(order_data)
        
        if pre_order_result.get("code") != 200:
            print(f"预下单失败: {pre_order_result.get('message')}")
            return
        
        # 从预下单结果中获取预下单索引ID（如果有的话）
        business_data = pre_order_result.get("business_data")
        if business_data and isinstance(business_data, str):
            business_data = json.loads(business_data)
            pre_create_order_tindex_id = business_data.get("pre_create_order_tindex_id")
            if pre_create_order_tindex_id:
                order_data["pre_create_order_tindex_id"] = pre_create_order_tindex_id
        
        # 2. 正式创建订单
        print("\n步骤2: 正式创建订单")
        create_result = client.create_order(order_data)
        
        if create_result.get("code") != 200:
            print(f"创建订单失败: {create_result.get('message')}")
            return
        
        # 获取蜂鸟订单号
        fengniao_order_no = None
        business_data = create_result.get("business_data")
        if business_data and isinstance(business_data, str):
            business_data = json.loads(business_data)
            fengniao_order_no = business_data.get("order_no")
        
        print(f"订单创建成功！蜂鸟订单号: {fengniao_order_no}")
        
        # 3. 查询订单详情
        print("\n步骤3: 查询订单详情")
        order_query = {
            "partner_order_code": partner_order_code
        }
        if fengniao_order_no:
            order_query["fengniao_order_no"] = fengniao_order_no
        
        detail_result = client.get_order_detail(order_query)
        
        # 4. 获取取消原因列表（可选）
        print("\n步骤4: 获取取消原因列表")
        cancel_reasons = client.get_cancel_reason_list()
        
        # 5. 预取消订单示例（可选，仅作演示）
        print("\n步骤5: 预取消订单示例")
        cancel_data = {
            "partner_order_code": partner_order_code,
            "cancel_reason_id": 1,  # 取消原因ID，需要从取消原因列表中获取
            "cancel_reason": "测试取消"
        }
        if fengniao_order_no:
            cancel_data["fengniao_order_no"] = fengniao_order_no
        
        pre_cancel_result = client.pre_cancel_order(cancel_data)
        
        print("\n=== API调用示例完成 ===")
        print("注意：以上示例仅用于演示API调用方法，实际使用时请根据业务需求调整参数")
        
    except Exception as e:
        print(f"\n执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

def test_health_check():
    """测试服务健康状态"""
    try:
        response = requests.get("http://localhost:8888/", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"服务状态: {result.get('message')}")
            return True
        else:
            print(f"服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"无法连接到服务: {str(e)}")
        print("请确保蜂鸟即配API服务已启动（默认端口8888）")
        return False

if __name__ == "__main__":
    print("检查服务状态...")
    if test_health_check():
        main()
    else:
        print("\n请先启动蜂鸟即配API服务：")
        print("cd /path/to/fengniao-fastapi-project")
        print("python -m app.main")
        print("或者")
        print("uvicorn app.main:app --host 0.0.0.0 --port 8888 --reload")